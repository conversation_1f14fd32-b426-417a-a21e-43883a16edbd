import pandas as pd
import glob
import sys
import json

from pathlib import PurePath

def main():
    root_dir = sys.argv[1]
    print(f"Importing from {root_dir}")
    json_files = sorted(glob.glob(root_dir + "/readings-*.json"))
    csv_files = sorted(glob.glob(root_dir + "/readings-*.csv"))

    metadata = json.load(open(json_files[0]))

    # read all csv files to dataframes and merge them
    batches = []
    dataframes = []
    for i, file_path in enumerate(csv_files):
        df = pd.read_csv(file_path, sep=",")
        time_start = json.load(open(file_path.replace(".csv", ".json")))["batch_start"]
        df["time"] = pd.to_datetime(time_start, format="%Y-%m-%d_%H-%M-%S") + pd.to_timedelta(df["time_offset"], unit="ms")
        df["batch_id"] = i
        batches.append({"start": df["time"].min().isoformat(), "end": df["time"].max().isoformat(), "file": file_path, "num_rows": len(df), "id": i})
        dataframes.append(df[["batch_id", "time", "x1", "y1", "z1", "x2", "y2", "z2"]])

    df = pd.concat(dataframes)
    print(df.head())

    out_dir = root_dir.replace("readings", "datasets")
    print(f"Writing to {out_dir}")

    # store metadata as json
    meta = {
        "batches": batches,
        "start": batches[0]["start"],
        "end": batches[-1]["end"],
        "num_batches": len(batches),
        "scenario": metadata["scenario"],
    }
    json.dump(meta, open(PurePath(out_dir, "dataset.json"), "w"))
    df.to_parquet(PurePath(out_dir, "data.parquet"))
    

if __name__ == "__main__":
    main()
