import pandas as pd
from dash import Dash, dcc, html
from dash.dependencies import Input, Output, State
import plotly.graph_objects as go
import eda

print("Loading dataset...")
df = eda.load_dataset("test-kk05")
print("Dataset loaded.")

# Default time window (5 minutes)
default_start = df.index.min()
default_end = default_start + pd.Timedelta(minutes=5)

# Initialize Dash with a simple stylesheet
external_stylesheets = ['https://codepen.io/chriddyp/pen/bWLwgP.css']
app = Dash(__name__, external_stylesheets=external_stylesheets)

# Helper for placeholder figures
def make_placeholder(title):
    fig = go.Figure()
    fig.update_layout(title=title)
    return fig

# Layout
app.layout = html.Div([
    html.H1('Sensor Acceleration Dashboard', style={'textAlign': 'center', 'marginBottom': '20px'}),
    html.Div([
        html.Div([
            html.Label('Start Time'),
            dcc.Input(
                id='start-time-picker', type='text',
                value=default_start.strftime('%Y-%m-%dT%H:%M')
            )
        ], style={'flex': '1', 'marginRight': '20px'}),
        html.Div([
            html.Label('End Time'),
            dcc.Input(
                id='end-time-picker', type='text',
                value=default_end.strftime('%Y-%m-%dT%H:%M')
            )
        ], style={'flex': '1', 'marginRight': '20px'}),
        html.Div([
            html.Button('Apply', id='apply-button', n_clicks=0, style={'width': '100%'})
        ], style={'flex': '0 0 80px', 'alignSelf': 'flex-end'})
    ], style={
        'display': 'flex', 'padding': '10px', 'border': '1px solid #ccc',
        'borderRadius': '5px', 'marginBottom': '20px', 'backgroundColor': '#f9f9f9'
    }),
    html.Div(id='error-message', style={'color': 'red', 'textAlign': 'center', 'marginBottom': '10px'}),
    dcc.Tabs([
        dcc.Tab(label='Sensor1 (x1,y1,z1)', children=[dcc.Graph(id='sensor1-graph')]),
        dcc.Tab(label='Sensor2 (x2,y2,z2)', children=[dcc.Graph(id='sensor2-graph')])
    ])
])

# Combined update callback
@app.callback(
    Output('sensor1-graph', 'figure'),
    Output('sensor2-graph', 'figure'),
    Output('error-message', 'children'),
    Input('apply-button', 'n_clicks'),
    State('start-time-picker', 'value'),
    State('end-time-picker', 'value')
)
def update_graphs(n_clicks, start_time, end_time):
    # placeholders
    placeholder1 = make_placeholder('Click Apply to load data')
    placeholder2 = make_placeholder('Click Apply to load data')
    if not n_clicks:
        return placeholder1, placeholder2, ''

    t0 = pd.to_datetime(start_time)
    t1 = pd.to_datetime(end_time)
    # enforce max 10-minute window
    if t1 - t0 > pd.Timedelta(minutes=10):
        return placeholder1, placeholder2, 'Please select a time range of 10 minutes or less.'

    # filter and plot
    dff = df[(df.index >= t0) & (df.index <= t1)]
    fig1 = go.Figure()
    for col in ['x1','y1','z1']:
        fig1.add_trace(go.Scattergl(x=dff.index, y=dff[col], mode='lines', name=col, opacity=0.6))
    fig1.update_layout(title=f"Sensor 1", xaxis_title='Time', yaxis_title='Value')

    fig2 = go.Figure()
    for col in ['x2','y2','z2']:
        fig2.add_trace(go.Scattergl(x=dff.index, y=dff[col], mode='lines', name=col, opacity=0.6))
    fig2.update_layout(title=f"Sensor 2", xaxis_title='Time', yaxis_title='Value')

    return fig1, fig2, ''

if __name__ == '__main__':
    app.run(debug=True)
