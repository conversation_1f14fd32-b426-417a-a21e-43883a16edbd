import pandas as pd

def load_dataset(name: str, sensor = None) -> pd.DataFrame:
    columns = ["time", "batch_id"]
    for axis in ["x", "y", "z"]:
        if sensor is not None:
            columns.append(f"{axis}{sensor}")
        else:
            columns.append(f"{axis}1")
            columns.append(f"{axis}2")
    df = pd.read_parquet(f"../datasets/{name}/data.parquet", columns=columns)
    df.set_index("time", inplace=True)

    if sensor is not None:
        df.rename({f"{axis}{sensor}": axis for axis in ["x", "y", "z"]}, axis=1, inplace=True)

    return df
