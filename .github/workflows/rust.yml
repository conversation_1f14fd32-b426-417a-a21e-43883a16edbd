name: Rust

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

env:
  CARGO_TERM_COLOR: always

jobs:
  build:

    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v4
    - name: Build
      run: cd labrecord && cargo build --release
    - name: Upload artifact
      uses: actions/upload-artifact@v4
      with:
        # Name of the artifact to upload.
        # Optional. Default is 'artifact'
        name: labrecord
    
        # A file, directory or wildcard pattern that describes what to upload
        # Required.
        path: labrecord/target/release/labrecord.exe
    
        # The desired behavior if no files are found using the provided path.
        # Available Options:
        #   warn: Output a warning but do not fail the action
        #   error: Fail the action with an error message
        #   ignore: Do not output any warnings or errors, the action does not fail
        # Optional. Default is 'warn'
        if-no-files-found: error
