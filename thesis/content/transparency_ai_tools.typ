#import "../utils/todo.typ": TODO

#TODO[
  Change this paragraph to reflect the tools you used in your thesis
]
/*
Categories of AI Usage:
  Grammar and Style Correction
    Tools: Grammarly, Hemingway
    Purpose: To correct grammatical errors, improve sentence structure, and enhance overall writing style.

  Translation and Language Enhancement
    Tools: DeepL, Google Translate
    Purpose: To translate texts or improve the quality and fluency of the writing in a different language.

  Content Generation and Idea Expansion
    Tools: ChatGPT, OpenAI Codex
    Purpose: To generate initial drafts, expand on ideas, provide suggestions for content, and offer examples.

  Coding Assistance
    Tools: GitHub Copilot
    Purpose: To help with coding tasks, generate code snippets, and provide programming solutions and explanations.

  Citation Assistance
    Tools: Citation Machine
    Purpose: To assist in proper citation formatting.
  
  Data Analysis and Visualization
    Tools: MATLAB, Python libraries (e.g., pandas, matplotlib), e.g. with ChatGPT or DataSpell
    Purpose: To assist in analyzing data sets, generating graphs, and visualizing data.

What You Need to Do
  - Use AI Tools Wisely: Feel encouraged to use AI tools for grammar correction, translation, content generation, coding assistance, plagiarism detection, data analysis and more, but make sure that you have the competencies to judge about the correctness and integrity of the generated content. You are responsible for the output!
  - Be Transparent: Include a short paragraph at the end of your proposal, thesis, seminar paper, project report, or any other text that is assessed describing how you used AI. Specify which tools you used, how extensively, for what purposes, and in which sections of your work you have used them.
  - Review: Make sure you review all text generated by AI tools and mention this as part of the transparency statement
*/

In preparing this thesis, I utilized Grammarly for grammar and style correction across the Abstract, Introduction, and Conclusion sections, ensuring clarity and coherence in my writing. I used DeepL to enhance language quality and translate parts of the Literature Review. I used ChatGPT to generate initial drafts and expand on ideas in the Introduction and Discussion sections, providing valuable suggestions and examples. Additionally, I used GitHub Copilot to generate code snippets for the developed functionality and code snippets in the Methodology section. I have carefully checked all texts created with these tools to ensure that they are correct and make sense.