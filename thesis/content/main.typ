#import "../utils/todo.typ": TODO

= Introduction
== Motivation
== Objective
== Structure of the Thesis

= Fundamentals
== Heat Pumps and Compressors
== Vibration Sensing
== Machine Learning for Fault Diagnosis

#TODO("explain this and that")

= Sensor Technology
== External Sensor + Microcontroller
== Smartphone Sensors

- not feasible due to temp

= Data Processing and Machine Learning
== Data Collection and Labeling

=== Important variables

- Sensor position

== Preprocessing and Feature Engineering
=== EDA

- Delete files with barely any data
- Remove parts with bad filestamps
- Convert to parquet

=== Model Comparison
=== Model Deployment

= Hardware Comparison
== Precision
== Cost
== Implementation Effort
== User-Friendliness

= Potential Extensions
== Additional Sensor Data

= Conclusion and Outlook
== Summary of Findings
== Potential Future Developments
