@article{nambiarPredictionAirCompressor2024,
  title = {Prediction of Air Compressor Faults with Feature Fusion and Machine Learning},
  author = {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and S., <PERSON><PERSON><PERSON> and S., Aravinth and V., Sugumaran and Ramteke, Sangharatna M. and <PERSON>, Max},
  date = {2024-11},
  journaltitle = {Knowledge-Based Systems},
  shortjournal = {Knowledge-Based Systems},
  volume = {304},
  pages = {112519},
  issn = {09507051},
  doi = {10.1016/j.knosys.2024.112519},
  url = {https://linkinghub.elsevier.com/retrieve/pii/S0950705124011535},
  urldate = {2025-05-26},
  abstract = {Air compressors are critical for many industries, but early detection of faults is crucial for keeping them running smoothly and minimizing maintenance costs. This contribution investigates the use of predictive machine learning models and feature fusion to diagnose faults in single-acting, single-stage reciprocating air compressors. Vibration signals acquired under healthy and different faulty conditions (inlet valve fluttering, outlet valve fluttering, inlet-outlet valve fluttering, and check valve fault) serve as the study’s input data. Diverse features including statistical attributes, histogram data, and auto-regressive moving average (ARMA) coefficients are extracted from the vibration signals. To identify the most relevant features, the J48 decision tree algorithm is employed. Five lazy classifiers viz. k-nearest neighbor (kNN), K-star, local kNN, locally weighted learning (LWL), and random subspace ensemble K-nearest neighbors (RseslibKnn) are then used for fault classification, each applied to the individual feature sets. The classifiers achieve commendable accuracy, ranging from 85.33\% (K-star and local kNN) to 96.00\% (RseslibKnn) for individual features. However, the true innovation lies in feature fusion. Combining the three feature types, statistical, histogram, and ARMA, significantly improves accuracy. When local kNN is used with fused features, the model achieves a remarkable 100\% classification accuracy, demonstrating the effectiveness of this approach for reliable fault diagnosis in air compressors.},
  langid = {english},
  file = {/Users/<USER>/Zotero/storage/SK2FP8DZ/Nambiar et al. - 2024 - Prediction of air compressor faults with feature fusion and machine learning.pdf}
}

@article{neupaneBearingFaultDetection2020,
  title = {Bearing {{Fault Detection}} and {{Diagnosis Using Case Western Reserve University Dataset With Deep Learning Approaches}}: {{A Review}}},
  shorttitle = {Bearing {{Fault Detection}} and {{Diagnosis Using Case Western Reserve University Dataset With Deep Learning Approaches}}},
  author = {Neupane, Dhiraj and Seok, Jongwon},
  date = {2020},
  journaltitle = {IEEE Access},
  volume = {8},
  pages = {93155--93178},
  issn = {2169-3536},
  doi = {10.1109/ACCESS.2020.2990528},
  url = {https://ieeexplore.ieee.org/document/9078761/},
  urldate = {2025-05-26},
  abstract = {A smart factory is a highly digitized and connected production facility that relies on smart manufacturing. Additionally, artificial intelligence is the core technology of smart factories. The use of machine learning and deep learning algorithms has produced fruitful results in many fields like image processing, speech recognition, fault detection, object detection, or medical sciences. With the increment in the use of smart machinery, the faults in the machinery equipment are expected to increase. Machinery fault detection and diagnosis through various deep learning algorithms has increased day by day. Many types of research have been done and published using both open-source and closed-source datasets, implementing the deep learning algorithms. Out of many publicly available datasets, Case Western Reserve University (CWRU) bearing dataset has been widely used to detect and diagnose machinery bearing fault and is accepted as a standard reference for validating the models. This paper summarizes the recent works which use the CWRU bearing dataset in machinery fault detection and diagnosis employing deep learning algorithms. We have reviewed the published works and presented the working algorithm, result, and other necessary details in this paper. This paper, we believe, can be of good help for future researchers to start their work on machinery fault detection and diagnosis using the CWRU dataset.},
  keywords = {Bearing,CWRU dataset,deep learning,Deep learning,Fault detection,Frequency-domain analysis,machine learning,Machinery,machinery fault detection and diagnosis,Vibrations,Wavelet transforms},
  file = {/Users/<USER>/Zotero/storage/GPANHC28/Neupane and Seok - 2020 - Bearing Fault Detection and Diagnosis Using Case Western Reserve University Dataset With Deep Learni.pdf}
}

@article{srivatsanFaultDiagnosisAir2024,
  title = {Fault Diagnosis of Air Compressors Using Transfer Learning: {{A}} Comparative Study of Pre-Trained Networks and Hyperparameter Optimization},
  shorttitle = {Fault Diagnosis of Air Compressors Using Transfer Learning},
  author = {Srivatsan, B and Naveen Venkatesh, S and Aravinth, S and Sugumaran, V and Arockia Dhanraj, Joshuva and Solomon, Jenoris Muthiya and Muthu Vaidhyanathan, R},
  date = {2024-12},
  journaltitle = {Journal of Low Frequency Noise, Vibration and Active Control},
  shortjournal = {Journal of Low Frequency Noise, Vibration and Active Control},
  volume = {43},
  number = {4},
  pages = {1877--1894},
  issn = {1461-3484, 2048-4046},
  doi = {10.1177/14613484241273652},
  url = {https://journals.sagepub.com/doi/10.1177/14613484241273652},
  urldate = {2025-05-26},
  abstract = {Air compressors are critical components in many industries whose catastrophic failure results in huge financial losses and downtime leading to accidents. Hence, real time fault diagnosis of air compressor is essential to predict the health condition of air compressor and plan scheduled maintenance thereby reducing financial losses and accidents. Fault diagnosis using transfer learning aids in real time fault detection. In the present study, five air compressor conditions were considered namely, check valve fault, inlet and outlet reed valve fluttering fault, inlet reed valve fluttering fault, outlet reed valve fluttering fault, and good condition. The raw vibration data was converted to radar plot images that were pre-processed and classified using four pre-trained networks (ResNet-50, GoogLeNet, AlexNet, and VGG-16). The hyperparameters like epochs, batch size, optimizer, train-test split ratio, and learning rate were varied to find out the best network for air compressor fault diagnosis. ResNet-50 among all other pre-trained networks produced the maximum classification accuracy (average of five trials) of 98.72\%.},
  langid = {english},
  file = {/Users/<USER>/Zotero/storage/9PWV6488/Srivatsan et al. - 2024 - Fault diagnosis of air compressors using transfer learning A comparative study of pre-trained netwo.pdf}
}
