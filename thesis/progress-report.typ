#import "layout/feedbacklog_template.typ": *
#import "metadata.typ": *
#import "utils/feedback.typ": *

#set document(title: titleEnglish, author: author)

#show: feedbacklog.with(
  titleEnglish: titleEnglish,
  supervisor: supervisor,
  advisors: advisors,
  author: author,
  presentationDate: presentationDate,
  feedbacklogSubmissionDate: feedbacklogSubmissionDate,
)

This document is a working document and progress report for the thesis. It is intended to be used as a feedback log and
to document the progress of the thesis.

It also serves to document ideas and thoughts that arise during the writing process.

#pagebreak()

#bibliography("thesis.bib", full: true)

#pagebreak()

= Time plan

#table(
  columns: (auto, auto, auto),
  inset: 5pt,
  align: horizon,
  table.header([*Phase*], [*Until*], [*Tasks*]),
  [Project start],
  [05/16],
  [Planning and prepare data collection],
  [Data collection and literature resarch],
  [06/13],
  [Collect data from good and bad compressor],
  [Feasibility study],
  [06/27],
  [Feasibility study with given data],
  [Break],
  [07/11],
  [Break],
  [Data processing],
  [07/25],
  [Preprocessing and feature engineering],
  [Train and compare models],
  [08/08],
  [Prototype model with Python],
  [Compare deployment options],
  [08/22],
  [Microcontroller vs phone vs cloud],
  [Model deployment],
  [09/05],
  [Deploy model on microcontroller],
  [User interface],
  [09/19],
  [Create user interface for displaying results],
  [Benchmark and optimize model],
  [10/03],
  [Benchmark and optimize algorithms / model],
  [Final report],
  [10/17],
  [Write final report],
  [Proof reading],
  [10/31],
  [Proof reading and final adjustments],
)

= Progress

== Week

- Plan KK05: Test higher sampling rates
  - Test highest technically possible
  - compressor rps is 90rps, so 200 is a bit low
- Test different sensor rotations
- Test different sensor positions

== Week

- Influence of delta p
- Influence of rps
- Good faith sensor positions

#pagebreak()

= Feedback

#feedback(
  feedback: "The structure of the presentation is rather unique; it would be great if you could help the audience to follow it easier.",
  response: "To help the audience to follow the presentation easier, a progress-bar was added at the bottom of the slides to show the milestones of the presentation and highlight the one that indicates the current slide.",
)