#![cfg(feature = "spi")]

use defmt::{debug, info};
use embassy_time::Timer;
use esp_hal::{spi::master::Spi, Async, Blocking}; // from embedded-hal-async

const REG_DATAX0: u8 = 0x32;
const REG_FIFO_STATUS: u8 = 0x39;
const REG_POWER_CTL: u8 = 0x2D;
const REG_DATA_FORMAT: u8 = 0x31;
const REG_BW_RATE: u8 = 0x2C;
const REG_FIFO_CTL: u8 = 0x38;

use crate::adxl::{SensorData, FIFO_SAMPLE_SIZE, TRANSFER_LEN};

/// SPI-reading task: `spi` is your already-configured SPI2 or SPI3
#[embassy_executor::task(pool_size = 2)]
pub async fn read_adxl_spi_task(spi: Spi<'static, Blocking>, pipe: SensorData) {
    // turn blocking SPI into async
    let mut spi = spi.into_async();
    let mut data_buf = [0u8; TRANSFER_LEN + 1];

    // init the sensor over SPI
    adxl345_spi_init(&mut spi).await;

    info!("Started ADXL345 SPI reading task on Core 1");

    loop {
        embassy_futures::yield_now().await;

        // 1) read FIFO_STATUS
        let mut status_cmd = [REG_FIFO_STATUS | 0x80, 0x00];
        spi.transfer_in_place_async(&mut status_cmd).await.unwrap();

        let fifo_count = status_cmd[1] & 0x3F;
        if fifo_count == 0 {
            Timer::after_millis(1).await;
            continue;
        }

        // 2) read up to TRANSFER_LEN bytes from DATAX0 (multi-byte read)
        let bytes_to_read = FIFO_SAMPLE_SIZE * (fifo_count as usize).min(4);
        data_buf.fill(0); // clear the buffer
        data_buf[0] = REG_DATAX0 | 0xC0; // 0x80 = read, 0x40 = multi

        spi.transfer_in_place_async(&mut data_buf).await.unwrap();

        debug!("Read {} bytes from FIFO", bytes_to_read);

        embassy_futures::yield_now().await;

        // 3) push into pipe
        pipe.write_all(&data_buf[1..bytes_to_read + 1]).await;
        debug!("Wrote {} bytes to pipe", bytes_to_read);
    }
}

/// SPI-based init sequence (same register writes as I2C version)
pub async fn adxl345_spi_init(spi: &mut Spi<'static, Async>) {
    // choose 200 Hz ODR
    let bw_rate = 0b1011;

    let writes = &[
        (REG_BW_RATE, bw_rate),
        (REG_DATA_FORMAT, 0b1001), // FULL_RES, ±4g
        (REG_FIFO_CTL, 0xDF),      // stream, watermark=31
        (REG_POWER_CTL, 0x08),     // measurement on
    ];

    for &(reg, val) in writes {
        let mut buf = [reg, val];
        spi.transfer_in_place_async(&mut buf).await.unwrap();
    }

    info!("ADXL345 initialized over SPI");
}
