#![cfg(feature = "i2c")]

use alloc::vec;
use defmt::{debug, info};
use embassy_time::Timer;
use esp_hal::{i2c::master::I2c, Async, Blocking};

const REG_DATAX0: u8 = 0x32;
const REG_FIFO_STATUS: u8 = 0x39;

// ADXL345 I2C Address
const ADXL345_ADDR: u8 = 0x53;

use crate::adxl::{SensorData, FIFO_SAMPLE_SIZE, TRANSFER_LEN};

#[embassy_executor::task(pool_size = 2)]
pub async fn read_adxl_i2c_task(i2c: I2c<'static, Blocking>, pipe: SensorData) {
    let mut i2c = i2c.into_async();
    let mut data_buf = vec![0u8; TRANSFER_LEN];

    adxl345_i2c_init(&mut i2c).await;

    info!("Started ADXL345 I2C reading task on Core 1");

    loop {
        embassy_futures::yield_now().await;

        let mut status = [0u8; 1];
        i2c.write_read_async(ADXL345_ADDR, &[REG_FIFO_STATUS], &mut status)
            .await
            .unwrap();
        let fifo_count = status[0] & 0x3F;
        if fifo_count == 0 {
            Timer::after_millis(1).await;
            continue;
        }
        let bytes_to_read = FIFO_SAMPLE_SIZE * 4.min(fifo_count as usize);

        i2c.write_read_async(ADXL345_ADDR, &[REG_DATAX0], &mut data_buf[0..bytes_to_read])
            .await
            .unwrap();
        debug!("Read {} bytes from FIFO", bytes_to_read);

        embassy_futures::yield_now().await;

        // Write the data to the pipe
        pipe.write_all(&data_buf[..bytes_to_read]).await;
        debug!("Wrote {} bytes to pipe", bytes_to_read);
    }
}

pub async fn adxl345_i2c_init(i2c: &mut I2c<'static, Async>) {
    const ADXL345_ADDR: u8 = 0x53;
    const REG_POWER_CTL: u8 = 0x2D;
    const REG_DATA_FORMAT: u8 = 0x31;
    const REG_BW_RATE: u8 = 0x2C;
    const REG_FIFO_CTL: u8 = 0x38;

    // 3200hz = bw_rate 0b1111
    // 1600hz = bw_rate 0b1110
    // 800hz = bw_rate 0b1101
    // 400hz = bw_rate 0b1100
    // 200hz = bw_rate 0b1011
    // and so on

    // 800hz
    let bw_rate = 0b1101;

    i2c.write_async(ADXL345_ADDR, &[REG_BW_RATE, bw_rate])
        .await
        .unwrap();

    // Set DATA_FORMAT to FULL_RES and +/-4g (0x0B)
    i2c.write_async(ADXL345_ADDR, &[REG_DATA_FORMAT, 0b1001])
        .await
        .unwrap();

    // Configure FIFO mode to stream mode with watermark of 31 samples (0xDF)
    // 0b11011111 = 0xDF (Stream mode with watermark of 31)
    i2c.write_async(ADXL345_ADDR, &[REG_FIFO_CTL, 0xDF])
        .await
        .unwrap();

    /*
    // Enable watermark interrupt on INT2 pin
    // 0x02 = Enable watermark interrupt
    i2c.write_async(ADXL345_ADDR, &[REG_INT_ENABLE, 0x02])
        .await
        .unwrap();

    // Map watermark interrupt to INT2 pin
    // 0x02 = Map watermark interrupt to INT2
    i2c.write_async(ADXL345_ADDR, &[REG_INT_MAP, 0x02])
        .await
        .unwrap();
    */

    // Start measurement (0x08)
    i2c.write_async(ADXL345_ADDR, &[REG_POWER_CTL, 0x08])
        .await
        .unwrap();

    info!("ADXL345 initialized over I2C");
}
