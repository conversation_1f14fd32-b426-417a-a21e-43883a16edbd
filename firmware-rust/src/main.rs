#![no_std]
#![no_main]

use core::ptr::addr_of_mut;

use defmt::info;
use embassy_time::{block_for, Duration};
use esp_hal::{interrupt::Priority, main, system::Stack};
use esp_hal_embassy::InterruptExecutor;
use panic_rtt_target as _;
use static_cell::StaticCell;

mod adxl;
mod i2c;
mod init;
mod spi;
mod uart;

extern crate alloc;

static mut APP_CORE_STACK: Stack<12192> = Stack::new();

#[main]
fn main() -> ! {
    // Initialize RTT for logging
    rtt_target::rtt_init_defmt!();
    info!("Starting firmware initialization on Core 0");

    block_for(Duration::from_millis(100));

    let (mut cpu_control, sw_ints, shared_drivers, core0_drivers, core1_drivers) = init::init();

    static EXECUTOR_CORE_0: StaticCell<InterruptExecutor<0>> = StaticCell::new();
    let executor_core0 = InterruptExecutor::new(sw_ints.software_interrupt0);
    let executor_core0 = EXECUTOR_CORE_0.init(executor_core0);

    info!("Starting UART task on Core 0 (PRO core)");
    let spawner = executor_core0.start(Priority::Priority1);
    spawner
        .spawn(uart::uart_tx_task(
            core1_drivers.uart,
            shared_drivers.sensor_pipe1.clone(),
            shared_drivers.sensor_pipe2.clone(),
        ))
        .unwrap();

    static EXECUTOR_CORE_1: StaticCell<InterruptExecutor<1>> = StaticCell::new();
    let executor_core1 = InterruptExecutor::new(sw_ints.software_interrupt1);
    let executor_core1 = EXECUTOR_CORE_1.init(executor_core1);

    info!("Starting app core");
    let _guard = cpu_control
        .start_app_core(unsafe { &mut *addr_of_mut!(APP_CORE_STACK) }, move || {
            let spawner = executor_core1.start(Priority::Priority1);

            info!("Starting sensor tasks on Core 1 (APP core)");
            #[cfg(feature = "i2c")]
            spawner
                .spawn(i2c::read_adxl_i2c_task(
                    core0_drivers.i2c1,
                    shared_drivers.sensor_pipe1.clone(),
                ))
                .unwrap();
            #[cfg(feature = "i2c")]
            spawner
                .spawn(i2c::read_adxl_i2c_task(
                    core0_drivers.i2c2,
                    shared_drivers.sensor_pipe2.clone(),
                ))
                .unwrap();
            #[cfg(feature = "spi")]
            spawner
                .spawn(spi::read_adxl_spi_task(
                    core0_drivers.spi1,
                    shared_drivers.sensor_pipe1.clone(),
                ))
                .unwrap();
            #[cfg(feature = "spi")]
            spawner
                .spawn(spi::read_adxl_spi_task(
                    core0_drivers.spi2,
                    shared_drivers.sensor_pipe2.clone(),
                ))
                .unwrap();

            // Just loop to show that the main thread does not need to poll the executor.
            loop {}
        })
        .unwrap();

    info!("All up and running!");

    // Just loop to show that the main thread does not need to poll the executor.
    loop {}
}
