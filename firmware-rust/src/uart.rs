use defmt::{debug, info};
use embassy_time::Instant;
use embedded_io_async::Write;
use esp_hal::Blocking;

use crate::adxl::{SensorData, FIFO_SAMPLE_SIZE};

// Embassy task version for Core 1
#[embassy_executor::task]
pub async fn uart_tx_task(
    uart: esp_hal::uart::Uart<'static, Blocking>,
    pipe1: SensorData,
    pipe2: SensorData,
) {
    let mut uart = uart.into_async();
    info!("Started UART TX task on Core 0");

    let mut buffer = [0u8; FIFO_SAMPLE_SIZE * 4];
    let mut last_stat = Instant::now();
    let mut num_samples = 0;

    loop {
        uart.write_all(b"START").await.unwrap();

        let mut i = 0;
        while i < buffer.len() {
            let n = pipe1.read(&mut buffer[i..]).await;
            i += n;
        }
        uart.write_all(&buffer).await.unwrap();

        let mut i = 0;
        while i < buffer.len() {
            let n = pipe2.read(&mut buffer[i..]).await;
            i += n;
        }
        uart.write_all(&buffer).await.unwrap();

        num_samples += 4;

        debug!("Wrote frame to UART");

        if last_stat.elapsed().as_secs() >= 1 {
            last_stat = Instant::now();
            info!("{} samples per second", num_samples);
            num_samples = 0;
        }
    }
}
