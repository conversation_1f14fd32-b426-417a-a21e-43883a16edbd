use alloc::sync::Arc;
use defmt::info;
use esp_hal::clock::CpuClock;
#[cfg(feature = "i2c")]
use esp_hal::i2c::{self, master::I2c};
use esp_hal::interrupt::software::SoftwareInterruptControl;
#[cfg(feature = "spi")]
use esp_hal::spi::master::Spi;
use esp_hal::system::CpuControl;
use esp_hal::time::Rate;
use esp_hal::timer::timg::TimerGroup;
use esp_hal::timer::AnyTimer;
use esp_hal::uart::Uart;
use esp_hal::Blocking;

use crate::adxl::SensorPipe;

// Struct to hold shared data between cores
pub struct Shared {
    pub sensor_pipe1: Arc<SensorPipe>,
    pub sensor_pipe2: Arc<SensorPipe>,
}

// Struct for Core 0 specific drivers
pub struct Core0Drivers {
    #[cfg(feature = "i2c")]
    pub i2c1: I2c<'static, Blocking>,
    #[cfg(feature = "i2c")]
    pub i2c2: I2c<'static, Blocking>,
    #[cfg(feature = "spi")]
    pub spi1: Spi<'static, Blocking>,
    #[cfg(feature = "spi")]
    pub spi2: Spi<'static, Blocking>,
}

// Struct for Core 1 specific drivers
pub struct Core1Drivers {
    pub uart: Uart<'static, Blocking>,
}

// Initialize the system and return all drivers
pub fn init() -> (
    CpuControl<'static>,
    SoftwareInterruptControl,
    Shared,
    Core0Drivers,
    Core1Drivers,
) {
    // Initialize the system
    let config = esp_hal::Config::default().with_cpu_clock(CpuClock::max());
    let peripherals = esp_hal::init(config);

    esp_alloc::heap_allocator!(size: 72 * 1024);

    let sw_ints = SoftwareInterruptControl::new(peripherals.SW_INTERRUPT);

    let timg0 = TimerGroup::new(peripherals.TIMG0);
    let timer0: AnyTimer = timg0.timer0.into();
    let timer1: AnyTimer = timg0.timer1.into();
    esp_hal_embassy::init([timer0, timer1]);
    info!("Timers initialized");

    info!("Embassy initialized!");

    // ADXL345 Wiring Guide:
    // Power connections:
    // - VCC → ESP32-S3 3.3V
    // - GND → ESP32-S3 GND

    // I2C connections
    #[cfg(feature = "i2c")]
    let sda = peripherals.GPIO17; // → ADXL345 SDA/SDI/SDIO
    #[cfg(feature = "i2c")]
    let scl = peripherals.GPIO15; // → ADXL345 SCL/SCLK

    // Initialize I2C
    #[cfg(feature = "i2c")]
    let i2c1 = I2c::new(
        peripherals.I2C1,
        i2c::master::Config::default().with_frequency(Rate::from_khz(400)),
    )
    .unwrap()
    .with_scl(scl)
    .with_sda(sda);

    #[cfg(feature = "i2c")]
    let sda = peripherals.GPIO12; // → ADXL345 SDA/SDI/SDIO
    #[cfg(feature = "i2c")]
    let scl = peripherals.GPIO13; // → ADXL345 SCL/SCLK
    #[cfg(feature = "i2c")]
    let i2c2 = I2c::new(
        peripherals.I2C0,
        i2c::master::Config::default().with_frequency(Rate::from_khz(400)),
    )
    .unwrap()
    .with_scl(scl)
    .with_sda(sda);

    // SPI connections
    #[cfg(feature = "spi")]
    let sclk = peripherals.GPIO12; // → ADXL345 SCL/SCLK
    #[cfg(feature = "spi")]
    let miso = peripherals.GPIO13; // → ADXL345 SDA/SDI/SDIO
    #[cfg(feature = "spi")]
    let mosi = peripherals.GPIO11; // → ADXL345 SDA/SDI/SDIO
    #[cfg(feature = "spi")]
    let cs = peripherals.GPIO10; // → ADXL345 CS
    #[cfg(feature = "spi")]
    let spi1 = Spi::new(
        peripherals.SPI2,
        esp_hal::spi::master::Config::default()
            .with_frequency(Rate::from_mhz(40))
            .with_mode(esp_hal::spi::Mode::_0),
    )
    .unwrap()
    .with_sck(sclk)
    .with_miso(miso)
    .with_mosi(mosi)
    .with_cs(cs);

    #[cfg(feature = "spi")]
    let sclk = peripherals.GPIO36; // → ADXL345 SCL/SCLK
    #[cfg(feature = "spi")]
    let miso = peripherals.GPIO37; // → ADXL345 SDA/SDI/SDIO
    #[cfg(feature = "spi")]
    let mosi = peripherals.GPIO35; // → ADXL345 SDA/SDI/SDIO
    #[cfg(feature = "spi")]
    let cs = peripherals.GPIO39; // → ADXL345 CS
    #[cfg(feature = "spi")]
    let spi2 = Spi::new(
        peripherals.SPI3,
        esp_hal::spi::master::Config::default()
            .with_frequency(Rate::from_mhz(40))
            .with_mode(esp_hal::spi::Mode::_0),
    )
    .unwrap()
    .with_sck(sclk)
    .with_miso(miso)
    .with_mosi(mosi)
    .with_cs(cs);

    // Initialize UART
    let tx_pin = peripherals.GPIO43;
    let rx_pin = peripherals.GPIO44;
    let uart_config = esp_hal::uart::Config::default();
    let uart = Uart::new(peripherals.UART0, uart_config)
        .unwrap()
        .with_tx(tx_pin)
        .with_rx(rx_pin);

    // Create the driver structs
    let shared_drivers = Shared {
        sensor_pipe1: Arc::new(SensorPipe::new()),
        sensor_pipe2: Arc::new(SensorPipe::new()),
    };

    let core0_drivers = Core0Drivers {
        #[cfg(feature = "i2c")]
        i2c1,
        #[cfg(feature = "i2c")]
        i2c2,
        #[cfg(feature = "spi")]
        spi1,
        #[cfg(feature = "spi")]
        spi2,
    };

    let core1_drivers = Core1Drivers { uart };

    // Return all driver structs
    (
        CpuControl::new(peripherals.CPU_CTRL),
        sw_ints,
        shared_drivers,
        core0_drivers,
        core1_drivers,
    )
}
