name: Continuous Integration

on:
  push:
    paths-ignore:
      - "**/README.md"
  pull_request:
  workflow_dispatch:

env:
  CARGO_TERM_COLOR: always
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  rust-checks:
    name: Rust Checks
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        action:
          - command: build
            args: --release
          - command: fmt
            args: --all -- --check
          - command: clippy
            args: --all-features --workspace -- -D warnings
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup Rust
        uses: esp-rs/xtensa-toolchain@v1.5
        with:
          default: true
          buildtargets: esp32s3
          ldproxy: false
      - name: Enable caching
        uses: Swatinem/rust-cache@v2
      - name: Run command
        run: cargo ${{ matrix.action.command }} ${{ matrix.action.args }}
