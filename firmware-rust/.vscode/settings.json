{"rust-analyzer.cargo.allTargets": false, "rust-analyzer.check.allTargets": false, "rust-analyzer.cargo.target": "xtensa-esp32s3-none-elf", "rust-analyzer.server.extraEnv": {"RUSTUP_TOOLCHAIN": "stable"}, "rust-analyzer.check.extraEnv": {"RUSTUP_TOOLCHAIN": "esp"}, "rust-analyzer.cargo.extraEnv": {"RUSTUP_TOOLCHAIN": "esp"}, "rust-analyzer.cargo.extraArgs": ["--target=xtensa-esp32s3-none-elf"], "rust-analyzer.check.extraArgs": ["--target=xtensa-esp32s3-none-elf"]}