{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "probe-rs-debug",
            "request": "launch",
            "name": "Launch",
            "cwd": "${workspaceFolder}",
            "chip": "esp32s3", //!MODIFY
            // probe field only needed if multiple probes connected. <Serial> is the MAC address of your esp in case of usb-jtag       
            //"probe": "VID:PID:<Serial>", //!MODIFY (or remove) | optional field
            "flashingConfig": {
                "flashingEnabled": true,
                "haltAfterReset": true,
                "formatOptions": {
                    "binaryFormat": "idf"
                }
            },
            "coreConfigs": [
                {
                    "coreIndex": 0,
                    "programBinary": "target/xtensa-esp32s3-none-elf/debug/${workspaceFolderBasename}",
                    // svdFiles describe the hardware register names off the esp peripherals, such as the LEDC peripheral. 
                    // They can be downloaded seperatly @ https://github.com/espressif/svd/tree/main/svd
                    //"svdFile": "${workspaceFolder}/esp32c3.svd" //!MODIFY (or remove) | optional field
                }
            ]
        },
        {
            "type": "probe-rs-debug",
            "request": "attach",
            "name": "Attach",
            "cwd": "${workspaceFolder}",
            "chip": "esp32s3",
            //"probe": "VID:PID:<Serial>", //!MODIFY (or remove) | optional field
            "coreConfigs": [
                {
                    "coreIndex": 0,
                    "programBinary": "target/xtensa-esp32s3-none-elf/debug/${workspaceFolderBasename}", //!MODIFY
                    //"svdFile": "${workspaceFolder}/esp32s3.svd" //!MODIFY (or remove) | optional field
                }
            ]
        }
    ]
}