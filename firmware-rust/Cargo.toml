[package]
edition = "2021"
name = "firmware"
version = "0.1.0"
license = "MIT OR Apache-2.0"

[features]
default = ["i2c"]
spi = []
i2c = []

[[bin]]
name = "firmware"
path = "./src/main.rs"
test = false
bench = false

[[test]]
name = "hello_test"
harness = false

[dependencies]
# esp hal dependencies
esp-alloc = "0.7.0"
esp-hal = { version = "1.0.0-beta.0", features = [
    "defmt",
    "esp32s3",
    "unstable",
] }
esp-hal-embassy = { version = "0.7.0", features = ["esp32s3"] }

# embassy dependencies
embassy-executor = { version = "0.7.0", features = [
    "defmt",
    "task-arena-size-20480",
] }
embassy-sync = "0.6.2"
embassy-time = { version = "0.4.0", features = ["generic-queue-8"] }
embassy-futures = "0.1.1"

# global hooks
critical-section = "1.2.0"
defmt = "0.3.10"
rtt-target = { version = "0.6.1", features = ["defmt"] }
panic-rtt-target = { version = "0.2.0", features = ["defmt"] }

# extensions
#embedded-hal-async = "1.0.0"
#embedded-io = "0.6.1"
embedded-io-async = "0.6.1"

# containers
heapless = { version = "0.8.0", default-features = false }
static_cell = { version = "2.1.0", features = ["nightly"] }

[dev-dependencies]
embedded-test = { version = "0.6.0", features = [
    "xtensa-semihosting",
    "embassy",
    "external-executor",
    "defmt",
] }

[profile.release]
debug = 2
lto = true
opt-level = 'z'

[profile.dev]
debug = 2
lto = true
opt-level = "z"
