#include <stdint.h>

#define BURST_SAMPLES 16

// Binary packet structure for efficient UART transmission
typedef struct __attribute__((packed))
{
    uint8_t header[4];               // "ADXL" magic header
    uint8_t sensor_id;               // Sensor ID (1 or 2)
    uint32_t timestamp;              // Timestamp
    uint8_t sample_count;            // Number of samples (should be 16)
    int16_t accel[BURST_SAMPLES][3]; // Raw accelerometer data
    uint16_t checksum;               // Simple checksum for data integrity
} uart_packet_t;
