use std::sync::{
    atomic::{AtomicUsize, Ordering},
    Arc,
};

use eframe::egui::{self, Color32};
use egui_plot::{Line, Plot, PlotPoint};
use tokio::sync::Mutex;

type Points = Vec<PlotPoint>;

pub fn run_gui(app: LabrecordApp) {
    let options = eframe::NativeOptions::default();
    eframe::run_native(
        "Sensor Readings",
        options,
        Box::new(|_cc| Ok(Box::new(app))),
    )
    .unwrap();
}

#[derive(Debug, Clone, Default)]
pub struct SensorData {
    pub x: Points,
    pub y: Points,
    pub z: Points,
}

impl SensorData {
    pub fn truncate(&mut self, num: usize) {
        if self.x.len() > num {
            self.x.drain(0..self.x.len() - num);
            self.y.drain(0..self.y.len() - num);
            self.z.drain(0..self.z.len() - num);
        }
    }
}

#[derive(<PERSON><PERSON>, Default)]
pub struct LabrecordApp {
    pub sensor1: Arc<Mutex<SensorData>>,
    pub sensor2: Arc<Mutex<SensorData>>,
    pub history_limit: Arc<AtomicUsize>,
}

impl eframe::App for LabrecordApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        egui::CentralPanel::default().show(ctx, |ui| {
            let mut sensor1 = self.sensor1.blocking_lock();
            let mut sensor2 = self.sensor2.blocking_lock();
            let mut history_limit_text = self.history_limit.load(Ordering::Relaxed).to_string();
            if ui
                .add(
                    egui::TextEdit::singleline(&mut history_limit_text)
                        .hint_text("History Limit")
                        .desired_width(100.0),
                )
                .changed()
            {
                if let Ok(new_limit) = history_limit_text.parse::<usize>() {
                    self.history_limit
                        .store(new_limit, std::sync::atomic::Ordering::Relaxed);
                }
            }
            let history_limit = self.history_limit.load(Ordering::Relaxed);
            sensor1.truncate(history_limit);
            sensor2.truncate(history_limit);
            Plot::new("Sensor 1").view_aspect(3.0).show(ui, |plot_ui| {
                plot_ui.line(Line::new("x", sensor1.x.as_slice()).color(Color32::RED));
                plot_ui.line(Line::new("y", sensor1.y.as_slice()).color(Color32::GREEN));
                plot_ui.line(Line::new("z", sensor1.z.as_slice()).color(Color32::BLUE));
            });
            Plot::new("Sensor 2").view_aspect(3.0).show(ui, |plot_ui| {
                plot_ui.line(Line::new("x", sensor2.x.as_slice()).color(Color32::RED));
                plot_ui.line(Line::new("y", sensor2.y.as_slice()).color(Color32::GREEN));
                plot_ui.line(Line::new("z", sensor2.z.as_slice()).color(Color32::BLUE));
            });
        });

        ctx.request_repaint(); // Continuous refresh
    }
}
