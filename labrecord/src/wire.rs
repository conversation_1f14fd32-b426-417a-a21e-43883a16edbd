use anyhow::Result;

use crate::packet::uart_packet_t;

pub fn parse_uart_packet(buffer: &[u8]) -> Result<uart_packet_t> {
    if buffer.len() != std::mem::size_of::<uart_packet_t>() {
        anyhow::bail!(
            "Invalid packet size: expected {}, got {}",
            std::mem::size_of::<uart_packet_t>(),
            buffer.len()
        );
    }

    let packet = unsafe { std::ptr::read_unaligned(buffer.as_ptr() as *const uart_packet_t) };

    if packet.header != [b'A', b'D', b'X', b'L'] {
        anyhow::bail!(
            "Invalid packet header: expected 'ADXL', got {:02X?} ('{}')",
            packet.header,
            String::from_utf8_lossy(&packet.header)
        );
    }

    Ok(packet)
}
