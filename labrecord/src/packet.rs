/* automatically generated by rust-bindgen 0.71.1 */

#[repr(C, packed)]
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct uart_packet_t {
    pub header: [u8; 4usize],
    pub sensor_id: u8,
    pub timestamp: u32,
    pub sample_count: u8,
    pub accel: [[i16; 3usize]; 16usize],
    pub checksum: u16,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of uart_packet_t"][::std::mem::size_of::<uart_packet_t>() - 108usize];
    ["Alignment of uart_packet_t"][::std::mem::align_of::<uart_packet_t>() - 1usize];
    ["Offset of field: uart_packet_t::header"]
        [::std::mem::offset_of!(uart_packet_t, header) - 0usize];
    ["Offset of field: uart_packet_t::sensor_id"]
        [::std::mem::offset_of!(uart_packet_t, sensor_id) - 4usize];
    ["Offset of field: uart_packet_t::timestamp"]
        [::std::mem::offset_of!(uart_packet_t, timestamp) - 5usize];
    ["Offset of field: uart_packet_t::sample_count"]
        [::std::mem::offset_of!(uart_packet_t, sample_count) - 9usize];
    ["Offset of field: uart_packet_t::accel"]
        [::std::mem::offset_of!(uart_packet_t, accel) - 10usize];
    ["Offset of field: uart_packet_t::checksum"]
        [::std::mem::offset_of!(uart_packet_t, checksum) - 106usize];
};
